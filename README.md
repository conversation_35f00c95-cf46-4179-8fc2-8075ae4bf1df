SplitEvaluator, NumericalSplitEvaluator and CategoricalSplitEvaluator
Next we implement the Split Evaluation by providing an interface that handles each type accordingly. For int/float we handle NumericalSplitEvaluator and for string we use CategoricalSplitEvaluator. We could use StringTypeSplitEvaluator rather than CategoricalSplitEvaluator, to keep things consistent and not confuse with the dataset type. 


// ====================
// Split Evaluation Strategy Pattern
// ====================

// SplitEvaluator handles different splitting algorithms
type SplitEvaluator[T comparable] interface {
    EvaluateSplits(view *DatasetView[T], column FeatureColumn, baseImpurity float64) ([]SplitCandidate, error)
}

// NumericalSplitEvaluator handles BOTH int and float features
type NumericalSplitEvaluator[T comparable] struct {
    impurityCalc ImpurityCalculator[T]
}

func (n *NumericalSplitEvaluator[T]) EvaluateSplits(
    view *DatasetView[T],      // OPERATES ON VIEW, NOT FULL DATASET
    column FeatureColumn, 
    baseImpurity float64,
) ([]SplitCandidate, error) {
    
    // Get sorted pairs from ONLY the active indices in the view
    sortedPairs, err := n.getSortedNumericalPairs(view, column)
    if err != nil {
        return nil, err
    }
    
    var candidates []SplitCandidate
    
    // Initialize target distributions for the VIEW's data only
    leftDist := make(map[T]int)
    rightDist := make(map[T]int)
    
    // All samples from VIEW start in right distribution
    for _, pair := range sortedPairs {
        rightDist[pair.target]++
    }
    
    // Sweep through sorted values (only active indices)
    for i := 0; i < len(sortedPairs)-1; i++ {
        currentPair := sortedPairs[i]
        
        // Move sample from right to left
        leftDist[currentPair.target]++
        rightDist[currentPair.target]--
        if rightDist[currentPair.target] == 0 {
            delete(rightDist, currentPair.target)
        }
        
        // Create split candidate if values are different
        if sortedPairs[i].value != sortedPairs[i+1].value {
            threshold := (sortedPairs[i].value + sortedPairs[i+1].value) / 2
            
            leftSize := i + 1
            rightSize := len(sortedPairs) - leftSize
            
            leftImpurity := n.impurityCalc.Calculate(leftDist, leftSize)
            rightImpurity := n.impurityCalc.Calculate(rightDist, rightSize)
            
            weightedImpurity := (float64(leftSize)*leftImpurity + float64(rightSize)*rightImpurity) / float64(view.size)
            gain := baseImpurity - weightedImpurity
            
            candidates = append(candidates, SplitCandidate{
                Type:      NumericalSplit,
                Threshold: &threshold,
                Gain:      gain,
                LeftSize:  leftSize,
                RightSize: rightSize,
            })
        }
    }
    
    return candidates, nil
}

// getSortedNumericalPairs works with VIEW's active indices only
func (n *NumericalSplitEvaluator[T]) getSortedNumericalPairs(view *DatasetView[T], column FeatureColumn) ([]numericalPair[T], error) {
    pairs := make([]numericalPair[T], view.size) // Only VIEW's size
    
    // Iterate through VIEW's active indices only
    for i := 0; i < view.size; i++ {
        physicalIndex := view.activeIndices[i] // Convert to physical index
        
        // Get numerical value from full dataset using physical index
        numValue, err := column.GetNumericalValue(physicalIndex)
        if err != nil {
            return nil, err
        }
        
        // Get target from view using logical index
        target, err := view.GetTarget(i)
        if err != nil {
            return nil, err
        }
        
        pairs[i] = numericalPair[T]{value: numValue, target: target}
    }
    
    // Sort by numerical value
    sort.Slice(pairs, func(i, j int) bool {
        return pairs[i].value < pairs[j].value
    })
    
    return pairs, nil
}


Similarly for CategoricalSplitEvaluator … 


// CategoricalSplitEvaluator handles string features using VIEW data
type CategoricalSplitEvaluator[T comparable] struct {
    impurityCalc ImpurityCalculator[T]
}

func (c *CategoricalSplitEvaluator[T]) EvaluateSplits(
    view *DatasetView[T],      // OPERATES ON VIEW, NOT FULL DATASET
    column FeatureColumn, 
    baseImpurity float64,
) ([]SplitCandidate, error) {
    
    // Get joint distribution from VIEW's active indices only
    jointDist, err := c.getStringJointDistribution(view, column)
    if err != nil {
        return nil, err
    }
    
    var candidates []SplitCandidate
    
    // For each unique string value in the VIEW, create a binary split candidate
    for stringValue, targetCounts := range jointDist {
        subsetSize := 0
        for _, count := range targetCounts {
            subsetSize += count
        }
        
        if subsetSize == 0 || subsetSize == view.size {
            continue // Skip pure splits
        }
        
        // Calculate split quality: "education == 'college'" vs "education != 'college'"
        leftImpurity := c.impurityCalc.Calculate(targetCounts, subsetSize)
        
        // Calculate right side (all other values in this VIEW)
        rightDist := c.calculateComplementDistribution(view, jointDist, stringValue)
        rightSize := view.size - subsetSize
        rightImpurity := c.impurityCalc.Calculate(rightDist, rightSize)
        
        weightedImpurity := (float64(subsetSize)*leftImpurity + float64(rightSize)*rightImpurity) / float64(view.size)
        gain := baseImpurity - weightedImpurity
        
        candidates = append(candidates, SplitCandidate{
            Type:      CategoricalSplit,
            Value:     stringValue,
            Gain:      gain,
            LeftSize:  subsetSize,
            RightSize: rightSize,
        })
    }
    
    return candidates, nil
}

// getStringJointDistribution calculates joint distribution for VIEW's active indices
func (c *CategoricalSplitEvaluator[T]) getStringJointDistribution(view *DatasetView[T], column FeatureColumn) (map[interface{}]map[T]int, error) {
    jointDist := make(map[interface{}]map[T]int)
    
    // Iterate through VIEW's active indices only
    for logicalIndex := 0; logicalIndex < view.size; logicalIndex++ {
        physicalIndex := view.activeIndices[logicalIndex]
        
        // Get feature value using physical index
        featureValue, err := column.GetValue(physicalIndex)
        if err != nil {
            return nil, err
        }
        
        // Get target using logical index within view
        target, err := view.GetTarget(logicalIndex)
        if err != nil {
            return nil, err
        }
        
        if jointDist[featureValue] == nil {
            jointDist[featureValue] = make(map[T]int)
        }
        jointDist[featureValue][target]++
    }
    
    return jointDist, nil
}

// calculateComplementDistribution calculates target distribution for all values != splitValue
func (c *CategoricalSplitEvaluator[T]) calculateComplementDistribution(view *DatasetView[T], jointDist map[interface{}]map[T]int, splitValue interface{}) map[T]int {
    rightDist := make(map[T]int)
    
    for featureValue, targetCounts := range jointDist {
        if featureValue != splitValue {
            for target, count := range targetCounts {
                rightDist[target] += count
            }
        }
    }
    
    return rightDist
}


Next we’ll have a Factory to return the correct type


// ====================
// Factory Pattern for All Three Types
// ====================

type SplitEvaluatorFactory[T comparable] struct {
    impurityCalc ImpurityCalculator[T]
}

func (f *SplitEvaluatorFactory[T]) CreateEvaluator(column FeatureColumn) SplitEvaluator[T] {
    switch column.GetType() {
    case IntegerFeature, FloatFeature:
        // BOTH int and float use numerical splitting
        return &NumericalSplitEvaluator[T]{impurityCalc: f.impurityCalc}
    case StringFeature:
        return &CategoricalSplitEvaluator[T]{impurityCalc: f.impurityCalc}
    default:
        return nil // or error
    }
}

// ====================
// Split Application for All Types
// ====================

// SplitCandidate represents potential splits for any feature type
type SplitCandidate struct {
    FeatureName string
    Type        SplitType
    
    // For numerical splits (int/float)
    Threshold   *float64     // "age <= 30.5" or "salary <= 65000.0"
    
    // For categorical splits (string)
    Value       interface{}  // "education == 'college'"
    
    Gain        float64
    LeftSize    int
    RightSize   int
}

// Apply split to create child dataset views
func (sc *SplitCandidate) ApplySplit(view *DatasetView[T]) (*DatasetView[T], *DatasetView[T], error) {
    switch sc.Type {
    case NumericalSplit:
        // Works for BOTH int and float columns
        return view.SplitByNumericalThreshold(sc.FeatureName, *sc.Threshold)
    case CategoricalSplit:
        // Works for string columns
        return view.SplitByStringValue(sc.FeatureName, sc.Value)
    default:
        return nil, nil, fmt.Errorf("unknown split type")
    }
}


Creating DatasetView based on Splits

Now lets look at how we create DatasetView splits based on the type of column. Numeric type should return two subset - left and right.


// ====================
// DatasetView Split Methods for All Types
// ====================

// SplitByNumericalThreshold works for BOTH int and float features
func (v *DatasetView[T]) SplitByNumericalThreshold(featureName string, threshold float64) (*DatasetView[T], *DatasetView[T], error) {
    column, err := v.dataset.GetColumn(featureName)
    if err != nil {
        return nil, nil, err
    }
    
    if !column.IsNumerical() {
        return nil, nil, fmt.Errorf("feature %s is not numerical", featureName)
    }
    
    var leftIndices, rightIndices []int
    
    for _, physicalIndex := range v.activeIndices {
        // GetNumericalValue() works for both IntColumn and FloatColumn
        numValue, err := column.GetNumericalValue(physicalIndex)
        if err != nil {
            continue // skip missing values
        }
        
        if numValue <= threshold {
            leftIndices = append(leftIndices, physicalIndex)
        } else {
            rightIndices = append(rightIndices, physicalIndex)
        }
    }
    
    leftView := v.dataset.CreateView(leftIndices)
    rightView := v.dataset.CreateView(rightIndices)
    
    return leftView, rightView, nil
}




In this example we are doing the same for StringValues. But we should actually return multiple branches (i.e. more than 2 DatasetViews, one for each string-value within the set of values being split)



// SplitByStringValue works for string features
func (v *DatasetView[T]) SplitByStringValue(featureName string, splitValue interface{}) (*DatasetView[T], *DatasetView[T], error) {
    column, err := v.dataset.GetColumn(featureName)
    if err != nil {
        return nil, nil, err
    }
    
    if column.GetType() != StringFeature {
        return nil, nil, fmt.Errorf("feature %s is not string", featureName)
    }
    
    splitString := splitValue.(string)
    var leftIndices, rightIndices []int
    
    for _, physicalIndex := range v.activeIndices {
        value, err := column.GetValue(physicalIndex)
        if err != nil {
            continue // skip missing values
        }
        
        stringValue := value.(string)
        if stringValue == splitString {
            leftIndices = append(leftIndices, physicalIndex)  // "education == 'college'"
        } else {
            rightIndices = append(rightIndices, physicalIndex) // "education != 'college'"
        }
    }
    
    leftView := v.dataset.CreateView(leftIndices)
    rightView := v.dataset.CreateView(rightIndices)
    
    return leftView, rightView, nil
}


Walkthrough Example

Now lets look at an example showing Dataset and DatasetView, with splitting


// ====================
// Complete Usage Example Showing Dataset vs DatasetView
// ====================

func CompleteExampleWithViews() {
    // Step 1: Create full dataset with all three types
    dataset := &Dataset[string]{
        intColumns: map[string]*IntColumn{
            "age": {data: []int64{25, 30, 35, 40, 22, 45}},
        },
        floatColumns: map[string]*FloatColumn{
            "salary": {data: []float64{50000.5, 75000.0, 60000.5, 80000.0, 45000.0, 90000.0}},
        },
        stringColumns: map[string]*StringColumn{
            "education": {data: []string{"college", "high_school", "college", "graduate", "high_school", "graduate"}},
        },
        targets: []string{"yes", "no", "yes", "yes", "no", "yes"}, // 6 samples total
        totalSize: 6,
    }
    
    // Step 2: Create root view (all data active)
    rootView := dataset.CreateView([]int{0, 1, 2, 3, 4, 5}) // All 6 rows active
    
    // Step 3: Calculate base impurity for the VIEW
    targetDist, _ := rootView.GetTargetDistribution()
    // targetDist = {"yes": 4, "no": 2} from VIEW's active indices only
    baseImpurity := entropyCalculator.Calculate(targetDist, rootView.size) // Uses VIEW size (6)
    
    // Step 4: Evaluate splits on the VIEW
    factory := &SplitEvaluatorFactory[string]{impurityCalc: entropyCalculator}
    
    var bestCandidate *SplitCandidate
    
    // Evaluate "age" (integer feature)
    ageColumn, _ := dataset.GetColumn("age")
    ageEvaluator := factory.CreateEvaluator(ageColumn) // Returns NumericalSplitEvaluator
    ageCandidates, _ := ageEvaluator.EvaluateSplits(rootView, ageColumn, baseImpurity)
    // ageCandidates might include: {Threshold: 27.5, Gain: 0.23} for "age <= 27.5"
    
    // Evaluate "salary" (float feature)  
    salaryColumn, _ := dataset.GetColumn("salary")
    salaryEvaluator := factory.CreateEvaluator(salaryColumn) // Returns NumericalSplitEvaluator
    salaryCandidates, _ := salaryEvaluator.EvaluateSplits(rootView, salaryColumn, baseImpurity)
    // salaryCandidates might include: {Threshold: 67500.0, Gain: 0.31} for "salary <= 67500.0"
    
    // Evaluate "education" (string feature)
    educationColumn, _ := dataset.GetColumn("education")
    educationEvaluator := factory.CreateEvaluator(educationColumn) // Returns CategoricalSplitEvaluator
    educationCandidates, _ := educationEvaluator.EvaluateSplits(rootView, educationColumn, baseImpurity)
    // educationCandidates might include: {Value: "college", Gain: 0.45} for "education == 'college'"
    
    // Step 5: Find best split across all types
    allCandidates := append(append(ageCandidates, salaryCandidates...), educationCandidates...)
    for _, candidate := range allCandidates {
        if bestCandidate == nil || candidate.Gain > bestCandidate.Gain {
            bestCandidate = &candidate
        }
    }
    
    // Step 6: Apply best split to create child views
    leftChildView, rightChildView, _ := bestCandidate.ApplySplit(rootView)
    
    // Example results if best split was "education == 'college'":
    // leftChildView.activeIndices = [0, 2]     // Rows where education == "college"
    // rightChildView.activeIndices = [1, 3, 4, 5] // Rows where education != "college"
    //
    // Both child views reference the SAME full dataset
    // They just have different activeIndices arrays
    
    // Step 7: Recursive tree building - each child view can be split further
    // This is where the memory efficiency shines - no data copying!
    
    // For left child (college students), recalculate distributions:
    leftTargetDist, _ := leftChildView.GetTargetDistribution()
    // leftTargetDist = {"yes": 2, "no": 0} calculated from activeIndices [0, 2] only
    
    // For right child (non-college), recalculate distributions:
    rightTargetDist, _ := rightChildView.GetTargetDistribution()  
    // rightTargetDist = {"yes": 2, "no": 2} calculated from activeIndices [1, 3, 4, 5] only
}


And then we’d simply wrap it up recursively building up the tree, creating DatasetViews to push down each recursive call. 

Apply Split Evaluation

During inference we’d do something like the following…


// SplitCondition defines how to evaluate if a value goes left or right
type SplitCondition interface {
    GoesLeft(value interface{}) (bool, error)
}

// NumericalCondition for threshold-based splits
type NumericalCondition struct {
    threshold float64
}

func (n *NumericalCondition) GoesLeft(value interface{}) (bool, error) {
    // Convert to numerical value
    numValue, err := convertToFloat64(value)
    if err != nil {
        return false, err
    }
    return numValue <= n.threshold, nil
}

// CategoricalCondition for value-based splits
type CategoricalCondition struct {
    splitValue string
}

func (c *CategoricalCondition) GoesLeft(value interface{}) (bool, error) {
    stringValue, ok := value.(string)
    if !ok {
        return false, fmt.Errorf("expected string, got %T", value)
    }
    return stringValue == c.splitValue, nil
}

// Single unified split method
func (v *DatasetView[T]) SplitByCondition(featureName string, condition SplitCondition) (*DatasetView[T], *DatasetView[T], error) {
    column, err := v.dataset.GetColumn(featureName)
    if err != nil {
        return nil, nil, err
    }
    
    var leftPhysicalIndices, rightPhysicalIndices []int
    
    // Single loop for all feature types
    for _, physicalIndex := range v.activeIndices {
        value, err := column.GetValue(physicalIndex)
        if err != nil {
            continue // skip missing values
        }
        
        goesLeft, err := condition.GoesLeft(value)
        if err != nil {
            continue // skip invalid values
        }
        
        if goesLeft {
            leftPhysicalIndices = append(leftPhysicalIndices, physicalIndex)
        } else {
            rightPhysicalIndices = append(rightPhysicalIndices, physicalIndex)
        }
    }
    
    leftChildView := v.dataset.CreateView(leftPhysicalIndices)
    rightChildView := v.dataset.CreateView(rightPhysicalIndices)
    
    return leftChildView, rightChildView, nil
}


func (sc *SplitCandidate) ApplySplit(view *DatasetView[T]) (*DatasetView[T], *DatasetView[T], error) {
    var condition SplitCondition
    
    switch sc.Type {
    case NumericalSplit:
        condition = &NumericalCondition{threshold: *sc.Threshold}
    case CategoricalSplit:
        condition = &CategoricalCondition{splitValue: sc.Value.(string)}
    default:
        return nil, nil, fmt.Errorf("unknown split type")
    }
    
    return view.SplitByCondition(sc.FeatureName, condition)
}


FeatureType, SplitType, FeatureInfo, sorting… etc
Other helper types that we’d need would then become

// ====================
// Helper Types
// ====================

type FeatureType int
const (
    IntegerFeature FeatureType = iota
    FloatFeature
    StringFeature
)

type SplitType int
const (
    NumericalSplit SplitType = iota   // For int/float: uses threshold
    CategoricalSplit                  // For string: uses specific value
)

type FeatureInfo struct {
    Name         string
    Type         FeatureType
    UniqueValues []interface{} // cached for categorical features
}

// Helper for numerical evaluator
type numericalPair[T comparable] struct {
    value  float64  // converted to float64 (works for both int and float)
    target T
}

// Helper to sort numeric values 
func (n *NumericalSplitEvaluator[T]) getSortedNumericalPairs(view *DatasetView[T], column FeatureColumn) ([]numericalPair[T], error) {
    pairs := make([]numericalPair[T], view.size)
    
    for i, physicalIndex := range view.activeIndices {
        // GetNumericalValue() converts both int64->float64 and float64->float64
        numValue, err := column.GetNumericalValue(physicalIndex)
        if err != nil {
            return nil, err
        }
        
        target, err := view.GetTarget(i)
        if err != nil {
            return nil, err
        }
        
        pairs[i] = numericalPair[T]{value: numValue, target: target}
    }
    
    // Sort by numerical value (works for both int and float)
    sort.Slice(pairs, func(i, j int) bool {
        return pairs[i].value < pairs[j].value
    })
    
    return pairs, nil
}


